// Waline DOM 结构调试脚本
// 在浏览器控制台中运行此脚本来检查Waline的DOM结构

function debugWalineDOM() {
  console.log('=== Waline DOM 结构调试 ===');
  
  const walineContainer = document.querySelector('.waline-wrapper-premium') || document.querySelector('[id*="waline"]');
  
  if (!walineContainer) {
    console.log('❌ 未找到Waline容器');
    return;
  }
  
  console.log('✅ 找到Waline容器:', walineContainer);
  
  // 检查登录/退出按钮
  const loginBtn = walineContainer.querySelector('.wl-login');
  const logoutBtn = walineContainer.querySelector('.wl-logout');
  
  console.log('登录按钮:', loginBtn);
  console.log('退出按钮:', logoutBtn);
  
  if (loginBtn) {
    console.log('登录按钮可见性:', loginBtn.offsetParent !== null);
    console.log('登录按钮样式:', window.getComputedStyle(loginBtn).display);
  }
  
  if (logoutBtn) {
    console.log('退出按钮可见性:', logoutBtn.offsetParent !== null);
    console.log('退出按钮样式:', window.getComputedStyle(logoutBtn).display);
  }
  
  // 检查用户信息
  const userElements = walineContainer.querySelectorAll('.wl-user');
  console.log('用户元素数量:', userElements.length);
  
  userElements.forEach((userEl, index) => {
    console.log(`用户元素 ${index + 1}:`, userEl);
    console.log(`  - 可见性:`, userEl.offsetParent !== null);
    console.log(`  - 父元素:`, userEl.parentElement);
    console.log(`  - 是否在评论列表中:`, userEl.closest('.wl-card') || userEl.closest('.wl-item') || userEl.closest('.wl-comment'));
    
    const nick = userEl.querySelector('.wl-nick');
    const avatar = userEl.querySelector('.wl-avatar');
    const link = userEl.querySelector('a[href]');
    
    if (nick) console.log(`  - 昵称:`, nick.textContent);
    if (avatar) {
      console.log(`  - 头像元素:`, avatar);
      const avatarImg = avatar.querySelector('img');
      if (avatarImg) {
        console.log(`  - 头像图片:`, avatarImg.src);
      } else {
        const bgImage = window.getComputedStyle(avatar).backgroundImage;
        console.log(`  - 头像背景:`, bgImage);
      }
    }
    if (link) console.log(`  - 链接:`, link.href);
  });
  
  // 检查状态栏
  const statusBar = walineContainer.querySelector('.wl-user-status-enhanced');
  console.log('增强状态栏:', statusBar);
  
  // 检查本地存储
  console.log('本地存储 WALINE_USER:', localStorage.getItem('WALINE_USER'));
  console.log('本地存储 waline-user:', localStorage.getItem('waline-user'));
  console.log('会话存储 WALINE_USER:', sessionStorage.getItem('WALINE_USER'));
  console.log('会话存储 waline-user:', sessionStorage.getItem('waline-user'));
}

// 运行调试
debugWalineDOM();

// 监听DOM变化
const observer = new MutationObserver((mutations) => {
  mutations.forEach((mutation) => {
    if (mutation.type === 'childList') {
      const addedNodes = Array.from(mutation.addedNodes);
      const hasUserElement = addedNodes.some(node => 
        node.nodeType === 1 && (
          node.classList?.contains('wl-user') || 
          node.querySelector?.('.wl-user')
        )
      );
      
      if (hasUserElement) {
        console.log('🔄 检测到用户元素变化，重新调试...');
        setTimeout(debugWalineDOM, 100);
      }
    }
  });
});

const walineContainer = document.querySelector('.waline-wrapper-premium') || document.querySelector('[id*="waline"]');
if (walineContainer) {
  observer.observe(walineContainer, {
    childList: true,
    subtree: true
  });
}

console.log('🔍 Waline调试脚本已启动，请在评论区进行登录/退出操作');
