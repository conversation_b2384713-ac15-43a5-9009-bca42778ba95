"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blogs/[slug]/page",{

/***/ "(app-pages-browser)/./src/components/comment/WalineComment.tsx":
/*!**************************************************!*\
  !*** ./src/components/comment/WalineComment.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalineComment: function() { return /* binding */ WalineComment; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _waline_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @waline/client */ \"(app-pages-browser)/./node_modules/@waline/client/dist/slim.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.module.js\");\n/* harmony import */ var _lib_walineConfig__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/walineConfig */ \"(app-pages-browser)/./src/lib/walineConfig.ts\");\n/* __next_internal_client_entry_do_not_use__ WalineComment,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction WalineComment({ path, title, className = \"\" }) {\n    _s();\n    const walineRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const { resolvedTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    const [walineInstance, setWalineInstance] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const initTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // 安全销毁实例的函数\n    const safeDestroy = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((instance)=>{\n        if (instance && typeof instance.destroy === \"function\") {\n            try {\n                instance.destroy();\n            } catch (error) {\n                console.warn(\"Waline destroy error:\", error);\n            }\n        }\n    }, []);\n    // 修复表情弹窗定位和层级问题\n    const fixEmojiPopups = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        if (!walineRef.current) return;\n        const container = walineRef.current;\n        // 修复表情弹窗\n        const emojiButtons = container.querySelectorAll(\"[data-waline] .wl-action\");\n        emojiButtons.forEach((button)=>{\n            if (button.textContent?.includes(\"\\uD83D\\uDE00\") || button.getAttribute(\"title\")?.includes(\"emoji\")) {\n                button.addEventListener(\"click\", (e)=>{\n                    setTimeout(()=>{\n                        const emojiPopup = container.querySelector(\"[data-waline] .wl-emoji-popup\");\n                        if (emojiPopup) {\n                            // 添加头部和关闭按钮\n                            if (!emojiPopup.querySelector(\".wl-popup-header\")) {\n                                const header = document.createElement(\"div\");\n                                header.className = \"wl-popup-header\";\n                                header.innerHTML = `\n                  <div class=\"wl-popup-title\">选择表情</div>\n                  <button class=\"wl-popup-close\" type=\"button\">×</button>\n                `;\n                                emojiPopup.insertBefore(header, emojiPopup.firstChild);\n                                // 添加关闭按钮事件\n                                const closeBtn = header.querySelector(\".wl-popup-close\");\n                                if (closeBtn) {\n                                    closeBtn.addEventListener(\"click\", ()=>{\n                                        emojiPopup.classList.remove(\"display\");\n                                    });\n                                }\n                            }\n                            // 添加背景点击关闭\n                            const handleBackgroundClick = (e)=>{\n                                if (e.target === emojiPopup) {\n                                    emojiPopup.classList.remove(\"display\");\n                                    document.removeEventListener(\"click\", handleBackgroundClick);\n                                }\n                            };\n                            setTimeout(()=>{\n                                document.addEventListener(\"click\", handleBackgroundClick);\n                            }, 100);\n                            // 添加ESC键关闭\n                            const handleEscKey = (e)=>{\n                                if (e.key === \"Escape\") {\n                                    emojiPopup.classList.remove(\"display\");\n                                    document.removeEventListener(\"keydown\", handleEscKey);\n                                }\n                            };\n                            document.addEventListener(\"keydown\", handleEscKey);\n                        }\n                    }, 50);\n                });\n            }\n        });\n        // 修复GIF弹窗（如果存在）\n        const gifButtons = container.querySelectorAll(\"[data-waline] .wl-action\");\n        gifButtons.forEach((button)=>{\n            if (button.textContent?.includes(\"GIF\") || button.getAttribute(\"title\")?.includes(\"gif\")) {\n                button.addEventListener(\"click\", (e)=>{\n                    setTimeout(()=>{\n                        const gifPopup = container.querySelector(\"[data-waline] .wl-gif-popup\");\n                        if (gifPopup) {\n                            // 添加头部和关闭按钮（如果不存在）\n                            if (!gifPopup.querySelector(\".wl-popup-header\")) {\n                                const header = document.createElement(\"div\");\n                                header.className = \"wl-popup-header\";\n                                header.innerHTML = `\n                  <div class=\"wl-popup-title\">选择GIF</div>\n                  <button class=\"wl-popup-close\" type=\"button\">×</button>\n                `;\n                                gifPopup.insertBefore(header, gifPopup.firstChild);\n                                // 添加关闭按钮事件\n                                const closeBtn = header.querySelector(\".wl-popup-close\");\n                                if (closeBtn) {\n                                    closeBtn.addEventListener(\"click\", ()=>{\n                                        gifPopup.classList.remove(\"display\");\n                                    });\n                                }\n                            }\n                        }\n                    }, 50);\n                });\n            }\n        });\n    }, []);\n    // 优化用户登录状态显示\n    const enhanceUserStatus = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        if (!walineRef.current) return;\n        const container = walineRef.current;\n        const loginBtn = container.querySelector(\".wl-login\");\n        const logoutBtn = container.querySelector(\".wl-logout\");\n        const panel = container.querySelector(\".wl-panel\");\n        if (!panel) return;\n        // 等待一小段时间确保Waline完全加载\n        setTimeout(()=>{\n            enhanceUserStatusInternal();\n        }, 100);\n    }, []);\n    const enhanceUserStatusInternal = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        if (!walineRef.current) return;\n        const container = walineRef.current;\n        const loginBtn = container.querySelector(\".wl-login\");\n        const logoutBtn = container.querySelector(\".wl-logout\");\n        const panel = container.querySelector(\".wl-panel\");\n        if (!panel) return;\n        // 获取用户信息（在隐藏之前）- 改进版本\n        let userInfo = {\n            nick: \"用户\",\n            avatar: \"\",\n            link: \"\"\n        };\n        // 1. 首先尝试从localStorage获取用户信息\n        try {\n            const storedUser = localStorage.getItem(\"WALINE_USER\") || localStorage.getItem(\"waline-user\");\n            if (storedUser) {\n                const userData = JSON.parse(storedUser);\n                if (userData.display_name || userData.nick) {\n                    userInfo.nick = userData.display_name || userData.nick;\n                }\n                if (userData.avatar) {\n                    userInfo.avatar = userData.avatar;\n                }\n                if (userData.url || userData.link) {\n                    userInfo.link = userData.url || userData.link;\n                }\n            }\n        } catch (e) {\n            console.log(\"无法读取localStorage中的用户信息\");\n        }\n        // 2. 从DOM中获取用户信息（作为补充或覆盖）\n        const userElements = container.querySelectorAll(\".wl-user\");\n        for (const userElement of userElements){\n            // 跳过评论列表中的用户信息\n            const isInCommentList = userElement.closest(\".wl-card\") || userElement.closest(\".wl-item\") || userElement.closest(\".wl-comment\");\n            if (isInCommentList) continue;\n            // 获取昵称\n            const nickElement = userElement.querySelector(\".wl-nick\");\n            if (nickElement && nickElement.textContent?.trim()) {\n                userInfo.nick = nickElement.textContent.trim();\n            }\n            // 获取头像\n            const avatarElement = userElement.querySelector(\".wl-avatar\");\n            if (avatarElement) {\n                const avatarImg = avatarElement.querySelector(\"img\");\n                if (avatarImg && avatarImg.src && !avatarImg.src.includes(\"data:\") && !avatarImg.src.includes(\"blob:\")) {\n                    userInfo.avatar = avatarImg.src;\n                } else {\n                    // 检查背景图片\n                    const bgImage = window.getComputedStyle(avatarElement).backgroundImage;\n                    if (bgImage && bgImage !== \"none\" && bgImage.includes(\"url(\")) {\n                        const url = bgImage.slice(5, -2) // 移除 url(\" 和 \")\n                        ;\n                        if (!url.includes(\"data:\") && !url.includes(\"blob:\")) {\n                            userInfo.avatar = url;\n                        }\n                    }\n                }\n            }\n            // 获取用户链接\n            const linkElement = userElement.querySelector(\"a[href]\") || userElement.closest(\"a[href]\");\n            if (linkElement && linkElement.href && !linkElement.href.includes(\"javascript:\")) {\n                userInfo.link = linkElement.href;\n            }\n            // 如果找到了有效信息就停止搜索\n            if (userInfo.nick !== \"用户\" || userInfo.avatar) {\n                break;\n            }\n        }\n        // 3. 如果还没有找到昵称，尝试从输入框获取\n        if (userInfo.nick === \"用户\") {\n            const nickInput = container.querySelector('input[placeholder*=\"nick\"], input[name=\"nick\"]');\n            if (nickInput && nickInput.value?.trim()) {\n                userInfo.nick = nickInput.value.trim();\n            }\n        }\n        console.log(\"提取的用户信息:\", userInfo);\n        // 添加头像点击事件跳转到用户中心\n        const addAvatarClickHandler = ()=>{\n            const userElements = container.querySelectorAll(\".wl-user\");\n            userElements.forEach((userElement)=>{\n                // 跳过评论列表中的用户信息\n                const isInCommentList = userElement.closest(\".wl-card\") || userElement.closest(\".wl-item\") || userElement.closest(\".wl-comment\");\n                if (!isInCommentList) {\n                    const avatar = userElement.querySelector(\".wl-avatar\");\n                    if (avatar && !avatar.hasAttribute(\"data-click-handler\")) {\n                        avatar.setAttribute(\"data-click-handler\", \"true\");\n                        avatar.style.cursor = \"pointer\";\n                        avatar.addEventListener(\"click\", async ()=>{\n                            // 获取Waline服务器URL\n                            const config = await (0,_lib_walineConfig__WEBPACK_IMPORTED_MODULE_4__.getWalineConfig)();\n                            const serverURL = config.serverURL;\n                            // 尝试从localStorage获取token\n                            let token = \"\";\n                            try {\n                                const storedUser = localStorage.getItem(\"WALINE_USER\") || localStorage.getItem(\"waline-user\");\n                                if (storedUser) {\n                                    const userData = JSON.parse(storedUser);\n                                    token = userData.token || \"\";\n                                }\n                            } catch (e) {\n                                console.log(\"无法获取用户token\");\n                            }\n                            // 构建用户中心URL\n                            const profileURL = token ? `${serverURL}/ui/profile?lng=en-US&token=${token}` : `${serverURL}/ui/login?lng=en-US`;\n                            window.open(profileURL, \"_blank\");\n                        });\n                    }\n                }\n            });\n        };\n        // 立即执行一次\n        addAvatarClickHandler();\n        // 定期检查新生成的用户元素\n        const handlerInterval = setInterval(addAvatarClickHandler, 500);\n        // 10秒后停止定期检查\n        setTimeout(()=>clearInterval(handlerInterval), 10000);\n        // 移除任何之前创建的自定义状态栏\n        const oldStatusBar = container.querySelector(\".wl-user-status-enhanced\");\n        if (oldStatusBar) {\n            oldStatusBar.remove();\n        }\n        // 添加悬停效果\n        const buttons = statusBar.querySelectorAll(\"button\");\n        buttons.forEach((btn)=>{\n            btn.addEventListener(\"mouseenter\", ()=>{\n                if (btn.classList.contains(\"wl-login-enhanced\")) {\n                    btn.style.background = \"hsl(var(--primary) / 0.9)\";\n                } else {\n                    btn.style.background = \"hsl(var(--primary) / 0.08)\";\n                    btn.style.borderColor = \"hsl(var(--primary) / 0.3)\";\n                    btn.style.color = \"hsl(var(--primary))\";\n                }\n                btn.style.transform = \"translateY(-1px)\";\n            });\n            btn.addEventListener(\"mouseleave\", ()=>{\n                if (btn.classList.contains(\"wl-login-enhanced\")) {\n                    btn.style.background = \"hsl(var(--primary))\";\n                } else {\n                    btn.style.background = \"transparent\";\n                    btn.style.borderColor = \"hsl(var(--border))\";\n                    btn.style.color = \"hsl(var(--muted-foreground))\";\n                }\n                btn.style.transform = \"translateY(0)\";\n            });\n        });\n    }, []);\n    // 初始化Waline的函数\n    const initWaline = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(async ()=>{\n        if (!walineRef.current) return;\n        // 取消之前的请求\n        if (abortControllerRef.current) {\n            abortControllerRef.current.abort();\n        }\n        // 创建新的AbortController\n        abortControllerRef.current = new AbortController();\n        try {\n            setIsLoading(true);\n            setError(null);\n            // 延迟初始化，避免快速切换导致的问题\n            await new Promise((resolve)=>setTimeout(resolve, 100));\n            // 检查组件是否仍然存在\n            if (!walineRef.current || abortControllerRef.current?.signal.aborted) {\n                return;\n            }\n            // 获取Waline配置\n            const config = await (0,_lib_walineConfig__WEBPACK_IMPORTED_MODULE_4__.getWalineConfig)();\n            // 清理容器\n            walineRef.current.innerHTML = \"\";\n            // 初始化Waline\n            const instance = (0,_waline_client__WEBPACK_IMPORTED_MODULE_1__.init)({\n                el: walineRef.current,\n                serverURL: config.serverURL,\n                path,\n                dark: resolvedTheme === \"dark\",\n                locale: {\n                    placeholder: \"Share your thoughts and join the discussion...\",\n                    admin: \"Admin\",\n                    level0: \"Newcomer\",\n                    level1: \"Explorer\",\n                    level2: \"Contributor\",\n                    level3: \"Expert\",\n                    level4: \"Master\",\n                    level5: \"Legend\",\n                    anonymous: \"Anonymous\",\n                    login: \"Sign In\",\n                    logout: \"Sign Out\",\n                    profile: \"Profile\",\n                    nickError: \"Nickname must be at least 3 characters\",\n                    mailError: \"Please enter a valid email address\",\n                    wordHint: \"Please enter your comment\",\n                    sofa: \"Be the first to share your thoughts!\",\n                    submit: \"Publish Comment\",\n                    reply: \"Reply\",\n                    cancelReply: \"Cancel Reply\",\n                    comment: \"Comment\",\n                    refresh: \"Refresh\",\n                    more: \"Load More Comments...\",\n                    preview: \"Preview\",\n                    emoji: \"Emoji\",\n                    uploadImage: \"Upload Image\",\n                    seconds: \"seconds ago\",\n                    minutes: \"minutes ago\",\n                    hours: \"hours ago\",\n                    days: \"days ago\",\n                    now: \"just now\"\n                },\n                emoji: [\n                    \"//unpkg.com/@waline/emojis@1.2.0/weibo\",\n                    \"//unpkg.com/@waline/emojis@1.2.0/alus\",\n                    \"//unpkg.com/@waline/emojis@1.2.0/bilibili\"\n                ],\n                meta: [\n                    \"nick\",\n                    \"mail\",\n                    \"link\"\n                ],\n                requiredMeta: [\n                    \"nick\"\n                ],\n                login: \"enable\",\n                wordLimit: [\n                    0,\n                    1000\n                ],\n                pageSize: 10,\n                lang: \"en-US\",\n                reaction: true,\n                imageUploader: false,\n                texRenderer: false,\n                search: false\n            });\n            if (!abortControllerRef.current?.signal.aborted) {\n                setWalineInstance(instance);\n                setIsInitialized(true);\n                setIsLoading(false);\n                // 添加加载完成的回调和修复表情弹窗\n                setTimeout(()=>{\n                    if (walineRef.current) {\n                        walineRef.current.classList.add(\"waline-loaded\");\n                        fixEmojiPopups();\n                        enhanceUserStatus();\n                    }\n                }, 300);\n            }\n        } catch (error) {\n            if (error instanceof Error && error.name !== \"AbortError\") {\n                console.error(\"Waline initialization error:\", error);\n                setError(\"Failed to load comments. Please refresh the page.\");\n                setIsLoading(false);\n            }\n        }\n    }, [\n        path,\n        resolvedTheme,\n        safeDestroy\n    ]);\n    // 主useEffect - 处理初始化和清理\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setIsInitialized(false);\n        // 使用setTimeout避免在React严格模式下的双重初始化\n        initTimeoutRef.current = setTimeout(()=>{\n            initWaline();\n        }, 50);\n        return ()=>{\n            // 清理timeout\n            if (initTimeoutRef.current) {\n                clearTimeout(initTimeoutRef.current);\n            }\n            // 取消请求\n            if (abortControllerRef.current) {\n                abortControllerRef.current.abort();\n            }\n            // 安全销毁实例\n            if (walineInstance) {\n                safeDestroy(walineInstance);\n            }\n            setIsInitialized(false);\n            setWalineInstance(null);\n            setIsLoading(true);\n            setError(null);\n        };\n    }, [\n        path,\n        resolvedTheme,\n        initWaline,\n        safeDestroy\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `waline-container-premium ${className}`,\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 bg-destructive/10 border border-destructive/20 rounded-xl text-destructive text-sm text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                        lineNumber: 473,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            setError(null);\n                            initWaline();\n                        },\n                        className: \"mt-2 px-3 py-1 bg-destructive/20 hover:bg-destructive/30 rounded-md transition-colors\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                        lineNumber: 474,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                lineNumber: 472,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"waline-container-premium\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    ref: walineRef,\n                    className: `waline-wrapper-premium transition-all duration-500 ${isInitialized ? \"opacity-100\" : \"opacity-0\"}`,\n                    style: {\n                        minHeight: isInitialized ? \"auto\" : \"300px\"\n                    }\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                    lineNumber: 488,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                lineNumber: 487,\n                columnNumber: 7\n            }, this),\n            isLoading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center py-16 space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 border-4 border-muted-foreground/20 border-t-primary rounded-full animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                                lineNumber: 503,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 w-12 h-12 border-4 border-transparent border-r-primary/50 rounded-full animate-spin\",\n                                style: {\n                                    animationDirection: \"reverse\",\n                                    animationDuration: \"0.8s\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                                lineNumber: 504,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                        lineNumber: 502,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-foreground\",\n                                children: \"Loading Discussion\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: \"Preparing comment system...\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                                lineNumber: 508,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                        lineNumber: 506,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                lineNumber: 501,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n        lineNumber: 469,\n        columnNumber: 5\n    }, this);\n}\n_s(WalineComment, \"nK4MtEyLhkL1QfmK3CntFo81j4c=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_3__.useTheme\n    ];\n});\n_c = WalineComment;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WalineComment);\nvar _c;\n$RefreshReg$(_c, \"WalineComment\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/comment/WalineComment.tsx\n"));

/***/ })

});