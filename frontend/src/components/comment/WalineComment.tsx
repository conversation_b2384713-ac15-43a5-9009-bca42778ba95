'use client'

import { init } from '@waline/client'
import { useEffect, useRef, useState, useCallback } from 'react'
import { useTheme } from 'next-themes'
import { getWalineConfig } from '@/lib/walineConfig'

interface WalineCommentProps {
  path: string
  title?: string
  className?: string
}

export function WalineComment({ path, title, className = '' }: WalineCommentProps) {
  const walineRef = useRef<HTMLDivElement>(null)
  const { resolvedTheme } = useTheme()
  const [walineInstance, setWalineInstance] = useState<any>(null)
  const [isInitialized, setIsInitialized] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const initTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const abortControllerRef = useRef<AbortController | null>(null)

  // 安全销毁实例的函数
  const safeDestroy = useCallback((instance: any) => {
    if (instance && typeof instance.destroy === 'function') {
      try {
        instance.destroy()
      } catch (error) {
        console.warn('Waline destroy error:', error)
      }
    }
  }, [])

  // 修复表情弹窗定位和层级问题
  const fixEmojiPopups = useCallback(() => {
    if (!walineRef.current) return

    const container = walineRef.current

    // 修复表情弹窗
    const emojiButtons = container.querySelectorAll('[data-waline] .wl-action')
    emojiButtons.forEach((button) => {
      if (button.textContent?.includes('😀') || button.getAttribute('title')?.includes('emoji')) {
        button.addEventListener('click', (e) => {
          setTimeout(() => {
            const emojiPopup = container.querySelector('[data-waline] .wl-emoji-popup')
            if (emojiPopup) {
              // 添加头部和关闭按钮
              if (!emojiPopup.querySelector('.wl-popup-header')) {
                const header = document.createElement('div')
                header.className = 'wl-popup-header'
                header.innerHTML = `
                  <div class="wl-popup-title">选择表情</div>
                  <button class="wl-popup-close" type="button">×</button>
                `
                emojiPopup.insertBefore(header, emojiPopup.firstChild)

                // 添加关闭按钮事件
                const closeBtn = header.querySelector('.wl-popup-close')
                if (closeBtn) {
                  closeBtn.addEventListener('click', () => {
                    emojiPopup.classList.remove('display')
                  })
                }
              }

              // 添加背景点击关闭
              const handleBackgroundClick = (e: Event) => {
                if (e.target === emojiPopup) {
                  emojiPopup.classList.remove('display')
                  document.removeEventListener('click', handleBackgroundClick)
                }
              }
              
              setTimeout(() => {
                document.addEventListener('click', handleBackgroundClick)
              }, 100)

              // 添加ESC键关闭
              const handleEscKey = (e: KeyboardEvent) => {
                if (e.key === 'Escape') {
                  emojiPopup.classList.remove('display')
                  document.removeEventListener('keydown', handleEscKey)
                }
              }
              document.addEventListener('keydown', handleEscKey)
            }
          }, 50)
        })
      }
    })

    // 修复GIF弹窗（如果存在）
    const gifButtons = container.querySelectorAll('[data-waline] .wl-action')
    gifButtons.forEach((button) => {
      if (button.textContent?.includes('GIF') || button.getAttribute('title')?.includes('gif')) {
        button.addEventListener('click', (e) => {
          setTimeout(() => {
            const gifPopup = container.querySelector('[data-waline] .wl-gif-popup')
            if (gifPopup) {
              // 添加头部和关闭按钮（如果不存在）
              if (!gifPopup.querySelector('.wl-popup-header')) {
                const header = document.createElement('div')
                header.className = 'wl-popup-header'
                header.innerHTML = `
                  <div class="wl-popup-title">选择GIF</div>
                  <button class="wl-popup-close" type="button">×</button>
                `
                gifPopup.insertBefore(header, gifPopup.firstChild)

                // 添加关闭按钮事件
                const closeBtn = header.querySelector('.wl-popup-close')
                if (closeBtn) {
                  closeBtn.addEventListener('click', () => {
                    gifPopup.classList.remove('display')
                  })
                }
              }
            }
          }, 50)
        })
      }
    })
  }, [])

  // 优化用户登录状态显示
  const enhanceUserStatus = useCallback(() => {
    if (!walineRef.current) return

    const container = walineRef.current
    const loginBtn = container.querySelector('.wl-login')
    const logoutBtn = container.querySelector('.wl-logout')
    const panel = container.querySelector('.wl-panel')

    if (!panel) return

    // 等待一小段时间确保Waline完全加载
    setTimeout(() => {
      enhanceUserStatusInternal()
    }, 100)
  }, [])

  const enhanceUserStatusInternal = useCallback(() => {
    if (!walineRef.current) return

    const container = walineRef.current
    const loginBtn = container.querySelector('.wl-login')
    const logoutBtn = container.querySelector('.wl-logout')
    const panel = container.querySelector('.wl-panel')

    if (!panel) return

    // 获取用户信息（在隐藏之前）- 改进版本
    let userInfo = {
      nick: '用户',
      avatar: '',
      link: ''
    }

    // 1. 首先尝试从localStorage获取用户信息
    try {
      const storedUser = localStorage.getItem('WALINE_USER') || localStorage.getItem('waline-user')
      if (storedUser) {
        const userData = JSON.parse(storedUser)
        if (userData.display_name || userData.nick) {
          userInfo.nick = userData.display_name || userData.nick
        }
        if (userData.avatar) {
          userInfo.avatar = userData.avatar
        }
        if (userData.url || userData.link) {
          userInfo.link = userData.url || userData.link
        }
      }
    } catch (e) {
      console.log('无法读取localStorage中的用户信息')
    }

    // 2. 从DOM中获取用户信息（作为补充或覆盖）
    const userElements = container.querySelectorAll('.wl-user')
    for (const userElement of userElements) {
      // 跳过评论列表中的用户信息
      const isInCommentList = userElement.closest('.wl-card') ||
                             userElement.closest('.wl-item') ||
                             userElement.closest('.wl-comment')
      if (isInCommentList) continue

      // 获取昵称
      const nickElement = userElement.querySelector('.wl-nick')
      if (nickElement && nickElement.textContent?.trim()) {
        userInfo.nick = nickElement.textContent.trim()
      }

      // 获取头像
      const avatarElement = userElement.querySelector('.wl-avatar')
      if (avatarElement) {
        const avatarImg = avatarElement.querySelector('img')
        if (avatarImg && avatarImg.src && !avatarImg.src.includes('data:') && !avatarImg.src.includes('blob:')) {
          userInfo.avatar = avatarImg.src
        } else {
          // 检查背景图片
          const bgImage = window.getComputedStyle(avatarElement).backgroundImage
          if (bgImage && bgImage !== 'none' && bgImage.includes('url(')) {
            const url = bgImage.slice(5, -2) // 移除 url(" 和 ")
            if (!url.includes('data:') && !url.includes('blob:')) {
              userInfo.avatar = url
            }
          }
        }
      }

      // 获取用户链接
      const linkElement = userElement.querySelector('a[href]') || userElement.closest('a[href]')
      if (linkElement && linkElement.href && !linkElement.href.includes('javascript:')) {
        userInfo.link = linkElement.href
      }

      // 如果找到了有效信息就停止搜索
      if (userInfo.nick !== '用户' || userInfo.avatar) {
        break
      }
    }

    // 3. 如果还没有找到昵称，尝试从输入框获取
    if (userInfo.nick === '用户') {
      const nickInput = container.querySelector('input[placeholder*="nick"], input[name="nick"]')
      if (nickInput && nickInput.value?.trim()) {
        userInfo.nick = nickInput.value.trim()
      }
    }

    console.log('提取的用户信息:', userInfo)

    // 添加头像点击事件跳转到用户中心
    const addAvatarClickHandler = () => {
      const userElements = container.querySelectorAll('.wl-user')
      userElements.forEach(userElement => {
        // 跳过评论列表中的用户信息
        const isInCommentList = userElement.closest('.wl-card') ||
                               userElement.closest('.wl-item') ||
                               userElement.closest('.wl-comment')

        if (!isInCommentList) {
          const avatar = userElement.querySelector('.wl-avatar')
          if (avatar && !avatar.hasAttribute('data-click-handler')) {
            avatar.setAttribute('data-click-handler', 'true')
            avatar.style.cursor = 'pointer'

            avatar.addEventListener('click', async () => {
              // 获取Waline服务器URL
              const config = await getWalineConfig()
              const serverURL = config.serverURL

              // 尝试从localStorage获取token
              let token = ''
              try {
                const storedUser = localStorage.getItem('WALINE_USER') || localStorage.getItem('waline-user')
                if (storedUser) {
                  const userData = JSON.parse(storedUser)
                  token = userData.token || ''
                }
              } catch (e) {
                console.log('无法获取用户token')
              }

              // 构建用户中心URL
              const profileURL = token ?
                `${serverURL}/ui/profile?lng=en-US&token=${token}` :
                `${serverURL}/ui/login?lng=en-US`

              window.open(profileURL, '_blank')
            })
          }
        }
      })
    }

    // 立即执行一次
    addAvatarClickHandler()

    // 定期检查新生成的用户元素
    const handlerInterval = setInterval(addAvatarClickHandler, 500)

    // 10秒后停止定期检查
    setTimeout(() => clearInterval(handlerInterval), 10000)

    // 移除任何之前创建的自定义状态栏
    const oldStatusBar = container.querySelector('.wl-user-status-enhanced')
    if (oldStatusBar) {
      oldStatusBar.remove()
    }









    // 添加悬停效果
    const buttons = statusBar.querySelectorAll('button')
    buttons.forEach(btn => {
      btn.addEventListener('mouseenter', () => {
        if (btn.classList.contains('wl-login-enhanced')) {
          btn.style.background = 'hsl(var(--primary) / 0.9)'
        } else {
          btn.style.background = 'hsl(var(--primary) / 0.08)'
          btn.style.borderColor = 'hsl(var(--primary) / 0.3)'
          btn.style.color = 'hsl(var(--primary))'
        }
        btn.style.transform = 'translateY(-1px)'
      })
      btn.addEventListener('mouseleave', () => {
        if (btn.classList.contains('wl-login-enhanced')) {
          btn.style.background = 'hsl(var(--primary))'
        } else {
          btn.style.background = 'transparent'
          btn.style.borderColor = 'hsl(var(--border))'
          btn.style.color = 'hsl(var(--muted-foreground))'
        }
        btn.style.transform = 'translateY(0)'
      })
    })
  }, [])

  // 初始化Waline的函数
  const initWaline = useCallback(async () => {
    if (!walineRef.current) return

    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    // 创建新的AbortController
    abortControllerRef.current = new AbortController()

    try {
      setIsLoading(true)
      setError(null)
      
      // 延迟初始化，避免快速切换导致的问题
      await new Promise(resolve => setTimeout(resolve, 100))

      // 检查组件是否仍然存在
      if (!walineRef.current || abortControllerRef.current?.signal.aborted) {
        return
      }

      // 获取Waline配置
      const config = await getWalineConfig()

      // 清理容器
      walineRef.current.innerHTML = ''

      // 初始化Waline
      const instance = init({
        el: walineRef.current,
        serverURL: config.serverURL,
        path,
        dark: resolvedTheme === 'dark',
        locale: {
          placeholder: 'Share your thoughts and join the discussion...',
          admin: 'Admin',
          level0: 'Newcomer',
          level1: 'Explorer',
          level2: 'Contributor', 
          level3: 'Expert',
          level4: 'Master',
          level5: 'Legend',
          anonymous: 'Anonymous',
          login: 'Sign In',
          logout: 'Sign Out',
          profile: 'Profile',
          nickError: 'Nickname must be at least 3 characters',
          mailError: 'Please enter a valid email address',
          wordHint: 'Please enter your comment',
          sofa: 'Be the first to share your thoughts!',
          submit: 'Publish Comment',
          reply: 'Reply',
          cancelReply: 'Cancel Reply',
          comment: 'Comment',
          refresh: 'Refresh',
          more: 'Load More Comments...',
          preview: 'Preview',
          emoji: 'Emoji',
          uploadImage: 'Upload Image',
          seconds: 'seconds ago',
          minutes: 'minutes ago',
          hours: 'hours ago',
          days: 'days ago',
          now: 'just now'
        },
        emoji: [
          '//unpkg.com/@waline/emojis@1.2.0/weibo',
          '//unpkg.com/@waline/emojis@1.2.0/alus',
          '//unpkg.com/@waline/emojis@1.2.0/bilibili',
        ],
        meta: ['nick', 'mail', 'link'],
        requiredMeta: ['nick'],
        login: 'enable',
        wordLimit: [0, 1000],
        pageSize: 10,
        lang: 'en-US',
        reaction: true,
        imageUploader: false,
        texRenderer: false,
        search: false
      })

      if (!abortControllerRef.current?.signal.aborted) {
        setWalineInstance(instance)
        setIsInitialized(true)
        setIsLoading(false)
        
        // 添加加载完成的回调和修复表情弹窗
        setTimeout(() => {
          if (walineRef.current) {
            walineRef.current.classList.add('waline-loaded')
            fixEmojiPopups()
            enhanceUserStatus()
          }
        }, 300)
      }
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error('Waline initialization error:', error)
        setError('Failed to load comments. Please refresh the page.')
        setIsLoading(false)
      }
    }
  }, [path, resolvedTheme, safeDestroy])

  // 主useEffect - 处理初始化和清理
  useEffect(() => {
    setIsInitialized(false)
    
    // 使用setTimeout避免在React严格模式下的双重初始化
    initTimeoutRef.current = setTimeout(() => {
      initWaline()
    }, 50)

    return () => {
      // 清理timeout
      if (initTimeoutRef.current) {
        clearTimeout(initTimeoutRef.current)
      }

      // 取消请求
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      // 安全销毁实例
      if (walineInstance) {
        safeDestroy(walineInstance)
      }

      setIsInitialized(false)
      setWalineInstance(null)
      setIsLoading(true)
      setError(null)
    }
  }, [path, resolvedTheme, initWaline, safeDestroy])

  return (
    <div className={`waline-container-premium ${className}`}>
      {/* 错误状态 */}
      {error && (
        <div className="mb-6 p-4 bg-destructive/10 border border-destructive/20 rounded-xl text-destructive text-sm text-center">
          <p>{error}</p>
          <button 
            onClick={() => {
              setError(null)
              initWaline()
            }}
            className="mt-2 px-3 py-1 bg-destructive/20 hover:bg-destructive/30 rounded-md transition-colors"
          >
            Retry
          </button>
        </div>
      )}
      
      {/* 主要的Waline容器 - 红点奖级别设计 */}
      <div className="waline-container-premium">
        <div
          ref={walineRef}
          className={`waline-wrapper-premium transition-all duration-500 ${
            isInitialized ? 'opacity-100' : 'opacity-0'
          }`}
          style={{
            minHeight: isInitialized ? 'auto' : '300px'
          } as React.CSSProperties}
        />
      </div>
      
      {/* 加载状态 */}
      {isLoading && !error && (
        <div className="flex flex-col items-center justify-center py-16 space-y-4">
          <div className="relative">
            <div className="w-12 h-12 border-4 border-muted-foreground/20 border-t-primary rounded-full animate-spin" />
            <div className="absolute inset-0 w-12 h-12 border-4 border-transparent border-r-primary/50 rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '0.8s' }} />
          </div>
          <div className="text-center space-y-1">
            <p className="text-sm font-medium text-foreground">Loading Discussion</p>
            <p className="text-xs text-muted-foreground">Preparing comment system...</p>
          </div>
        </div>
      )}
    </div>
  )
}

export default WalineComment