'use client'

import { useState } from 'react'
import { WalineCommentPremium } from '@/components/comment/WalineCommentPremium'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Palette, 
  Sparkles, 
  Award, 
  Eye, 
  MessageCircle, 
  Heart,
  Zap,
  Star,
  Layers,
  Brush
} from 'lucide-react'

export default function WalineDesignDemo() {
  const [activeDemo, setActiveDemo] = useState('premium')

  const designFeatures = [
    {
      icon: <Award className="w-5 h-5" />,
      title: "Red Dot Award Level",
      description: "世界级设计标准，获得国际认可的审美水准"
    },
    {
      icon: <Palette className="w-5 h-5" />,
      title: "Design Token System",
      description: "统一的设计令牌系统，确保视觉一致性"
    },
    {
      icon: <Sparkles className="w-5 h-5" />,
      title: "Glass Morphism",
      description: "现代玻璃态设计，高级质感与视觉层次"
    },
    {
      icon: <Zap className="w-5 h-5" />,
      title: "Micro Interactions",
      description: "精致的微交互动画，提升用户体验"
    },
    {
      icon: <Layers className="w-5 h-5" />,
      title: "Multi-layer Shadows",
      description: "多层阴影系统，营造立体空间感"
    },
    {
      icon: <Brush className="w-5 h-5" />,
      title: "Premium Typography",
      description: "高级字体排版，优化阅读体验"
    }
  ]

  const colorTokens = [
    { name: "Primary", value: "hsl(var(--primary))", description: "主品牌色" },
    { name: "Primary Soft", value: "hsl(var(--primary) / 0.08)", description: "柔和主色" },
    { name: "Surface Glass", value: "hsl(var(--background) / 0.85)", description: "玻璃表面" },
    { name: "Border Subtle", value: "hsl(var(--border) / 0.2)", description: "精致边框" }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        {/* 页面标题 */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Award className="w-8 h-8 text-primary" />
            <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              Waline Red Dot Design
            </h1>
            <Award className="w-8 h-8 text-primary" />
          </div>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            红点奖级别的评论区设计系统 - 世界级审美标准与用户体验
          </p>
          <div className="flex items-center justify-center gap-2 mt-4">
            <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20">
              <Star className="w-3 h-3 mr-1" />
              Red Dot Award Level
            </Badge>
            <Badge variant="secondary" className="bg-emerald-500/10 text-emerald-600 border-emerald-500/20">
              <Eye className="w-3 h-3 mr-1" />
              Premium Design
            </Badge>
            <Badge variant="secondary" className="bg-blue-500/10 text-blue-600 border-blue-500/20">
              <Sparkles className="w-3 h-3 mr-1" />
              Glass Morphism
            </Badge>
          </div>
        </div>

        <Tabs value={activeDemo} onValueChange={setActiveDemo} className="space-y-8">
          <TabsList className="grid w-full grid-cols-3 max-w-md mx-auto">
            <TabsTrigger value="premium">Premium Demo</TabsTrigger>
            <TabsTrigger value="features">Design Features</TabsTrigger>
            <TabsTrigger value="tokens">Design Tokens</TabsTrigger>
          </TabsList>

          <TabsContent value="premium" className="space-y-8">
            {/* 设计展示卡片 */}
            <Card className="border-2 border-primary/20 bg-gradient-to-br from-card to-card/50 backdrop-blur-sm">
              <CardHeader className="text-center">
                <CardTitle className="flex items-center justify-center gap-2">
                  <MessageCircle className="w-5 h-5 text-primary" />
                  Premium Comment System
                </CardTitle>
                <CardDescription>
                  体验红点奖级别的评论区设计，感受世界级的视觉美学与交互体验
                </CardDescription>
              </CardHeader>
              <CardContent>
                {/* 用户体验改进说明 */}
                <div className="mb-6 p-4 bg-gradient-to-r from-primary/5 to-primary/10 rounded-lg border border-primary/20">
                  <h4 className="font-semibold text-sm mb-3 flex items-center gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full"></span>
                    ✨ Enhanced User Experience
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-muted-foreground">
                    <div className="flex items-start gap-2">
                      <span className="text-green-500 mt-0.5">✓</span>
                      <span>智能状态栏：未登录时完全隐藏，登录时显示真实用户信息</span>
                    </div>
                    <div className="flex items-start gap-2">
                      <span className="text-green-500 mt-0.5">✓</span>
                      <span>真实头像显示：32px适当尺寸，支持点击跳转到用户主页</span>
                    </div>
                    <div className="flex items-start gap-2">
                      <span className="text-green-500 mt-0.5">✓</span>
                      <span>完美登录检测：准确识别登录状态，避免不必要的界面元素</span>
                    </div>
                    <div className="flex items-start gap-2">
                      <span className="text-green-500 mt-0.5">✓</span>
                      <span>可靠退出登录：多重保障机制，确保退出功能完全可用</span>
                    </div>
                  </div>
                </div>

                {/* Waline评论区演示 */}
                <WalineCommentPremium
                  path="/demo/waline-design"
                  title="Red Dot Design Demo"
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="features" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {designFeatures.map((feature, index) => (
                <Card key={index} className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border-border/50">
                  <CardHeader>
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-lg bg-primary/10 text-primary group-hover:bg-primary group-hover:text-white transition-colors">
                        {feature.icon}
                      </div>
                      <CardTitle className="text-lg">{feature.title}</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-sm leading-relaxed">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="tokens" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Palette className="w-5 h-5" />
                  Design Token System
                </CardTitle>
                <CardDescription>
                  统一的设计令牌系统，确保整个评论区的视觉一致性
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {colorTokens.map((token, index) => (
                    <div key={index} className="flex items-center gap-4 p-4 rounded-lg border bg-card/50">
                      <div 
                        className="w-12 h-12 rounded-lg border-2 border-border/20"
                        style={{ backgroundColor: token.value }}
                      />
                      <div className="flex-1">
                        <h4 className="font-medium">{token.name}</h4>
                        <p className="text-sm text-muted-foreground">{token.description}</p>
                        <code className="text-xs bg-muted px-2 py-1 rounded mt-1 inline-block">
                          {token.value}
                        </code>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* 底部说明 */}
        <div className="mt-16 text-center">
          <Card className="bg-gradient-to-r from-primary/5 to-primary/10 border-primary/20">
            <CardContent className="pt-6">
              <div className="flex items-center justify-center gap-2 mb-4">
                <Award className="w-6 h-6 text-primary" />
                <h3 className="text-xl font-semibold">Red Dot Award Design Philosophy</h3>
              </div>
              <p className="text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                这个评论区设计遵循红点奖的设计理念：功能性与美学的完美结合。
                通过精心设计的视觉层次、优雅的动画效果和直观的交互体验，
                为用户创造一个既美观又实用的评论环境。
              </p>
              <div className="flex items-center justify-center gap-4 mt-6">
                <Badge variant="outline" className="border-primary/30 text-primary">
                  <Heart className="w-3 h-3 mr-1" />
                  User-Centered Design
                </Badge>
                <Badge variant="outline" className="border-primary/30 text-primary">
                  <Sparkles className="w-3 h-3 mr-1" />
                  Aesthetic Excellence
                </Badge>
                <Badge variant="outline" className="border-primary/30 text-primary">
                  <Zap className="w-3 h-3 mr-1" />
                  Innovative Interaction
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
