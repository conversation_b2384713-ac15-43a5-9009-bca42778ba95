/* Waline Red Dot Award Design System */
/* 红点奖级别的评论区设计系统 - 世界级审美标准 */

/* === 设计令牌系统 === */
.waline-wrapper-premium {
  /* 色彩令牌 */
  --waline-primary: hsl(var(--primary));
  --waline-primary-soft: hsl(var(--primary) / 0.08);
  --waline-primary-medium: hsl(var(--primary) / 0.15);
  --waline-primary-strong: hsl(var(--primary) / 0.9);
  
  /* 表面色彩 */
  --waline-surface-primary: hsl(var(--background));
  --waline-surface-secondary: hsl(var(--card));
  --waline-surface-elevated: hsl(var(--background) / 0.98);
  --waline-surface-glass: hsl(var(--background) / 0.85);
  
  /* 边框与分割线 */
  --waline-border-subtle: hsl(var(--border) / 0.2);
  --waline-border-medium: hsl(var(--border) / 0.4);
  --waline-border-strong: hsl(var(--border) / 0.8);
  
  /* 文本层次 */
  --waline-text-primary: hsl(var(--foreground));
  --waline-text-secondary: hsl(var(--muted-foreground));
  --waline-text-tertiary: hsl(var(--muted-foreground) / 0.7);
  --waline-text-inverse: hsl(var(--background));
  
  /* 间距系统 */
  --waline-space-xs: 0.25rem;
  --waline-space-sm: 0.5rem;
  --waline-space-md: 0.75rem;
  --waline-space-lg: 1rem;
  --waline-space-xl: 1.5rem;
  --waline-space-2xl: 2rem;
  --waline-space-3xl: 3rem;
  
  /* 圆角系统 */
  --waline-radius-sm: 0.5rem;
  --waline-radius-md: 0.75rem;
  --waline-radius-lg: 1rem;
  --waline-radius-xl: 1.5rem;
  --waline-radius-2xl: 2rem;
  
  /* 阴影系统 */
  --waline-shadow-subtle: 0 2px 8px rgba(0, 0, 0, 0.04);
  --waline-shadow-medium: 0 8px 24px rgba(0, 0, 0, 0.08);
  --waline-shadow-strong: 0 16px 48px rgba(0, 0, 0, 0.12);
  --waline-shadow-glow: 0 0 32px var(--waline-primary-soft);
  
  /* 动画系统 */
  --waline-transition-fast: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --waline-transition-medium: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --waline-transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
  --waline-transition-bounce: 400ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* === 主容器设计 === */
.waline-container-premium {
  margin: var(--waline-space-3xl) 0;
  width: 100%;
  position: relative;
  isolation: isolate;
}

/* === 核心包装器设计 === */
.waline-wrapper-premium {
  width: 100%;
  max-width: none;
  position: relative;
  
  /* 高级玻璃态背景 */
  background: 
    linear-gradient(135deg, 
      var(--waline-surface-elevated) 0%,
      var(--waline-surface-glass) 50%,
      var(--waline-surface-elevated) 100%
    );
  
  /* 精致边框系统 */
  border: 1px solid var(--waline-border-subtle);
  border-radius: var(--waline-radius-2xl);
  
  /* 多层阴影系统 */
  box-shadow: 
    var(--waline-shadow-medium),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.02);
  
  /* 高级滤镜效果 */
  backdrop-filter: blur(16px) saturate(1.2) brightness(1.05);
  -webkit-backdrop-filter: blur(16px) saturate(1.2) brightness(1.05);
  
  /* 内边距系统 */
  padding: var(--waline-space-2xl) var(--waline-space-xl);
  
  /* 流畅过渡 */
  transition: 
    transform var(--waline-transition-medium),
    box-shadow var(--waline-transition-medium),
    border-color var(--waline-transition-medium);
  
  /* 层叠上下文 */
  isolation: isolate;
  overflow: hidden;
}

/* === 装饰性元素 === */
.waline-wrapper-premium::before {
  content: '';
  position: absolute;
  top: 0;
  left: 15%;
  right: 15%;
  height: 2px;
  background: linear-gradient(
    90deg, 
    transparent 0%, 
    var(--waline-primary-medium) 20%,
    var(--waline-primary) 50%,
    var(--waline-primary-medium) 80%,
    transparent 100%
  );
  border-radius: 1px;
  opacity: 0.8;
  animation: shimmer 3s ease-in-out infinite;
}

.waline-wrapper-premium::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle at center,
    var(--waline-primary-soft) 0%,
    transparent 50%
  );
  opacity: 0;
  transition: opacity var(--waline-transition-slow);
  pointer-events: none;
  z-index: -1;
}

/* === 悬停交互效果 === */
.waline-wrapper-premium:hover {
  transform: translateY(-4px) scale(1.002);
  border-color: var(--waline-border-medium);
  
  box-shadow: 
    var(--waline-shadow-strong),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.15),
    var(--waline-shadow-glow);
}

.waline-wrapper-premium:hover::after {
  opacity: 1;
}

/* === 动画关键帧 === */
@keyframes shimmer {
  0%, 100% { 
    opacity: 0.6; 
    transform: translateX(-10px);
  }
  50% { 
    opacity: 1; 
    transform: translateX(10px);
  }
}

/* === 响应式设计 === */
@media (max-width: 768px) {
  .waline-wrapper-premium {
    padding: var(--waline-space-xl) var(--waline-space-lg);
    border-radius: var(--waline-radius-xl);
  }
  
  .waline-container-premium {
    margin: var(--waline-space-2xl) 0;
  }
}

@media (max-width: 480px) {
  .waline-wrapper-premium {
    padding: var(--waline-space-lg) var(--waline-space-md);
    border-radius: var(--waline-radius-lg);
  }
  
  .waline-wrapper-premium:hover {
    transform: translateY(-2px) scale(1.001);
  }
}

/* === 可访问性优化 === */
@media (prefers-reduced-motion: reduce) {
  .waline-wrapper-premium,
  .waline-wrapper-premium::before,
  .waline-wrapper-premium::after {
    animation: none;
    transition: none;
  }
  
  .waline-wrapper-premium:hover {
    transform: none;
  }
}

/* === 高对比度模式 === */
@media (prefers-contrast: high) {
  .waline-wrapper-premium {
    border-color: var(--waline-border-strong);
    background: var(--waline-surface-primary);
  }
}

/* === 暗色模式优化 === */
@media (prefers-color-scheme: dark) {
  .waline-wrapper-premium {
    backdrop-filter: blur(16px) saturate(1.1) brightness(0.95);
    -webkit-backdrop-filter: blur(16px) saturate(1.1) brightness(0.95);
  }

  .waline-wrapper-premium::before {
    opacity: 0.6;
  }
}

/* === 评论输入区域设计 === */
.waline-wrapper-premium .wl-panel {
  background: var(--waline-surface-secondary) !important;
  border: 1px solid var(--waline-border-subtle) !important;
  border-radius: var(--waline-radius-xl) !important;
  padding: var(--waline-space-xl) !important;
  margin-bottom: var(--waline-space-xl) !important;
  box-shadow: var(--waline-shadow-subtle) !important;
  transition: all var(--waline-transition-medium) !important;
  position: relative !important;
  overflow: hidden !important;
}

.waline-wrapper-premium .wl-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    var(--waline-primary-medium) 50%,
    transparent 100%
  );
  opacity: 0.5;
}

.waline-wrapper-premium .wl-panel:focus-within {
  border-color: var(--waline-primary) !important;
  box-shadow:
    var(--waline-shadow-medium),
    0 0 0 3px var(--waline-primary-soft) !important;
  transform: translateY(-1px) !important;
}

/* === 文本编辑器设计 === */
.waline-wrapper-premium .wl-editor {
  background: var(--waline-surface-primary) !important;
  border: 1px solid var(--waline-border-subtle) !important;
  border-radius: var(--waline-radius-lg) !important;
  color: var(--waline-text-primary) !important;
  font-size: 1rem !important;
  line-height: 1.6 !important;
  padding: var(--waline-space-lg) !important;
  min-height: 120px !important;
  resize: vertical !important;
  transition: all var(--waline-transition-medium) !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

.waline-wrapper-premium .wl-editor:focus {
  outline: none !important;
  border-color: var(--waline-primary) !important;
  box-shadow:
    0 0 0 3px var(--waline-primary-soft),
    var(--waline-shadow-subtle) !important;
  background: var(--waline-surface-elevated) !important;
}

.waline-wrapper-premium .wl-editor::placeholder {
  color: var(--waline-text-tertiary) !important;
  font-style: italic !important;
}

/* === 工具栏设计 === */
.waline-wrapper-premium .wl-action {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  margin-top: var(--waline-space-lg) !important;
  padding-top: var(--waline-space-lg) !important;
  border-top: 1px solid var(--waline-border-subtle) !important;
}

.waline-wrapper-premium .wl-action .wl-btn {
  background: var(--waline-primary) !important;
  color: var(--waline-text-inverse) !important;
  border: none !important;
  border-radius: var(--waline-radius-md) !important;
  padding: var(--waline-space-sm) var(--waline-space-lg) !important;
  font-weight: 500 !important;
  font-size: 0.875rem !important;
  cursor: pointer !important;
  transition: all var(--waline-transition-medium) !important;
  box-shadow: var(--waline-shadow-subtle) !important;
  position: relative !important;
  overflow: hidden !important;
}

.waline-wrapper-premium .wl-action .wl-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 100%
  );
  transition: left var(--waline-transition-medium);
}

.waline-wrapper-premium .wl-action .wl-btn:hover {
  background: var(--waline-primary-strong) !important;
  transform: translateY(-1px) !important;
  box-shadow: var(--waline-shadow-medium) !important;
}

.waline-wrapper-premium .wl-action .wl-btn:hover::before {
  left: 100%;
}

.waline-wrapper-premium .wl-action .wl-btn:active {
  transform: translateY(0) !important;
  box-shadow: var(--waline-shadow-subtle) !important;
}

/* === 表情和工具按钮 === */
.waline-wrapper-premium .wl-action .wl-emoji,
.waline-wrapper-premium .wl-action .wl-image {
  background: var(--waline-surface-secondary) !important;
  border: 1px solid var(--waline-border-subtle) !important;
  border-radius: var(--waline-radius-sm) !important;
  color: var(--waline-text-secondary) !important;
  padding: var(--waline-space-sm) !important;
  cursor: pointer !important;
  transition: all var(--waline-transition-fast) !important;
  margin-right: var(--waline-space-sm) !important;
}

.waline-wrapper-premium .wl-action .wl-emoji:hover,
.waline-wrapper-premium .wl-action .wl-image:hover {
  background: var(--waline-primary-soft) !important;
  border-color: var(--waline-primary-medium) !important;
  color: var(--waline-primary) !important;
  transform: translateY(-1px) !important;
}

/* === 用户信息输入区域 === */
.waline-wrapper-premium .wl-info {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
  gap: var(--waline-space-md) !important;
  margin-bottom: var(--waline-space-lg) !important;
}

.waline-wrapper-premium .wl-info input {
  background: var(--waline-surface-primary) !important;
  border: 1px solid var(--waline-border-subtle) !important;
  border-radius: var(--waline-radius-md) !important;
  color: var(--waline-text-primary) !important;
  padding: var(--waline-space-sm) var(--waline-space-md) !important;
  font-size: 0.875rem !important;
  transition: all var(--waline-transition-medium) !important;
}

.waline-wrapper-premium .wl-info input:focus {
  outline: none !important;
  border-color: var(--waline-primary) !important;
  box-shadow: 0 0 0 2px var(--waline-primary-soft) !important;
  background: var(--waline-surface-elevated) !important;
}

.waline-wrapper-premium .wl-info input::placeholder {
  color: var(--waline-text-tertiary) !important;
}

/* === 评论列表设计 === */
.waline-wrapper-premium .wl-cards {
  margin-top: var(--waline-space-2xl) !important;
}

.waline-wrapper-premium .wl-card {
  background: var(--waline-surface-secondary) !important;
  border: 1px solid var(--waline-border-subtle) !important;
  border-radius: var(--waline-radius-xl) !important;
  padding: var(--waline-space-xl) !important;
  margin-bottom: var(--waline-space-lg) !important;
  transition: all var(--waline-transition-medium) !important;
  position: relative !important;
  overflow: hidden !important;
}

.waline-wrapper-premium .wl-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(
    180deg,
    var(--waline-primary) 0%,
    var(--waline-primary-medium) 100%
  );
  opacity: 0;
  transition: opacity var(--waline-transition-medium);
}

.waline-wrapper-premium .wl-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: var(--waline-shadow-medium) !important;
  border-color: var(--waline-border-medium) !important;
}

.waline-wrapper-premium .wl-card:hover::before {
  opacity: 1;
}

/* === 用户头像设计 === */
/* 隐藏所有默认的用户信息显示，除了评论列表中的 */
.waline-wrapper-premium .wl-user {
  display: none !important;
}

.waline-wrapper-premium .wl-user .wl-avatar {
  width: 48px !important;
  height: 48px !important;
  border-radius: 50% !important;
  margin-right: var(--waline-space-md) !important;
  border: 2px solid var(--waline-border-subtle) !important;
  transition: all var(--waline-transition-medium) !important;
  box-shadow: var(--waline-shadow-subtle) !important;
}

.waline-wrapper-premium .wl-user .wl-avatar:hover {
  border-color: var(--waline-primary) !important;
  box-shadow:
    var(--waline-shadow-medium),
    0 0 0 2px var(--waline-primary-soft) !important;
  transform: scale(1.05) !important;
}

.waline-wrapper-premium .wl-user .wl-nick {
  font-weight: 600 !important;
  color: var(--waline-text-primary) !important;
  font-size: 1rem !important;
  margin-bottom: var(--waline-space-xs) !important;
  text-decoration: none !important;
  transition: color var(--waline-transition-fast) !important;
}

.waline-wrapper-premium .wl-user .wl-nick:hover {
  color: var(--waline-primary) !important;
}

.waline-wrapper-premium .wl-user .wl-badge {
  background: var(--waline-primary) !important;
  color: var(--waline-text-inverse) !important;
  font-size: 0.75rem !important;
  padding: 2px 8px !important;
  border-radius: 12px !important;
  margin-left: var(--waline-space-sm) !important;
  font-weight: 500 !important;
}

/* === 评论时间和元信息 === */
.waline-wrapper-premium .wl-meta {
  display: flex !important;
  align-items: center !important;
  gap: var(--waline-space-md) !important;
  color: var(--waline-text-tertiary) !important;
  font-size: 0.875rem !important;
  margin-bottom: var(--waline-space-lg) !important;
}

.waline-wrapper-premium .wl-time {
  color: var(--waline-text-tertiary) !important;
  font-size: 0.875rem !important;
  transition: color var(--waline-transition-fast) !important;
}

.waline-wrapper-premium .wl-time:hover {
  color: var(--waline-text-secondary) !important;
}

/* === 评论内容设计 === */
.waline-wrapper-premium .wl-content {
  color: var(--waline-text-primary) !important;
  line-height: 1.7 !important;
  font-size: 1rem !important;
  margin-bottom: var(--waline-space-lg) !important;
}

.waline-wrapper-premium .wl-content p {
  margin-bottom: var(--waline-space-md) !important;
}

.waline-wrapper-premium .wl-content a {
  color: var(--waline-primary) !important;
  text-decoration: none !important;
  border-bottom: 1px solid transparent !important;
  transition: all var(--waline-transition-fast) !important;
}

.waline-wrapper-premium .wl-content a:hover {
  border-bottom-color: var(--waline-primary) !important;
}

.waline-wrapper-premium .wl-content code {
  background: var(--waline-surface-primary) !important;
  color: var(--waline-primary) !important;
  padding: 2px 6px !important;
  border-radius: 4px !important;
  font-size: 0.875rem !important;
  border: 1px solid var(--waline-border-subtle) !important;
}

.waline-wrapper-premium .wl-content blockquote {
  border-left: 4px solid var(--waline-primary) !important;
  background: var(--waline-surface-primary) !important;
  padding: var(--waline-space-md) var(--waline-space-lg) !important;
  margin: var(--waline-space-lg) 0 !important;
  border-radius: 0 var(--waline-radius-md) var(--waline-radius-md) 0 !important;
  font-style: italic !important;
  color: var(--waline-text-secondary) !important;
}

/* === 评论操作按钮 === */
.waline-wrapper-premium .wl-action-group {
  display: flex !important;
  align-items: center !important;
  gap: var(--waline-space-md) !important;
  padding-top: var(--waline-space-lg) !important;
  border-top: 1px solid var(--waline-border-subtle) !important;
}

.waline-wrapper-premium .wl-like,
.waline-wrapper-premium .wl-reply-btn {
  background: transparent !important;
  border: 1px solid var(--waline-border-subtle) !important;
  color: var(--waline-text-secondary) !important;
  padding: var(--waline-space-sm) var(--waline-space-md) !important;
  border-radius: var(--waline-radius-md) !important;
  font-size: 0.875rem !important;
  cursor: pointer !important;
  transition: all var(--waline-transition-fast) !important;
  display: flex !important;
  align-items: center !important;
  gap: var(--waline-space-xs) !important;
}

.waline-wrapper-premium .wl-like:hover,
.waline-wrapper-premium .wl-reply-btn:hover {
  background: var(--waline-primary-soft) !important;
  border-color: var(--waline-primary-medium) !important;
  color: var(--waline-primary) !important;
  transform: translateY(-1px) !important;
}

.waline-wrapper-premium .wl-like.active {
  background: var(--waline-primary) !important;
  border-color: var(--waline-primary) !important;
  color: var(--waline-text-inverse) !important;
}

/* === 回复嵌套设计 === */
.waline-wrapper-premium .wl-quote {
  background: var(--waline-surface-primary) !important;
  border: 1px solid var(--waline-border-subtle) !important;
  border-radius: var(--waline-radius-lg) !important;
  padding: var(--waline-space-lg) !important;
  margin-left: var(--waline-space-2xl) !important;
  margin-top: var(--waline-space-lg) !important;
  position: relative !important;
}

.waline-wrapper-premium .wl-quote::before {
  content: '';
  position: absolute;
  left: -var(--waline-space-lg);
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--waline-primary) !important;
  border-radius: 1px;
}

/* === 表情选择器设计 === */
.waline-wrapper-premium .wl-emoji-popup {
  background: var(--waline-surface-secondary) !important;
  border: 1px solid var(--waline-border-medium) !important;
  border-radius: var(--waline-radius-xl) !important;
  box-shadow:
    var(--waline-shadow-strong),
    0 0 0 1px rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(20px) saturate(1.2) !important;
  -webkit-backdrop-filter: blur(20px) saturate(1.2) !important;
  padding: var(--waline-space-lg) !important;
  max-height: 300px !important;
  overflow-y: auto !important;
  z-index: 1000 !important;
}

.waline-wrapper-premium .wl-emoji-popup .wl-emoji-item {
  padding: var(--waline-space-sm) !important;
  border-radius: var(--waline-radius-sm) !important;
  cursor: pointer !important;
  transition: all var(--waline-transition-fast) !important;
  font-size: 1.25rem !important;
}

.waline-wrapper-premium .wl-emoji-popup .wl-emoji-item:hover {
  background: var(--waline-primary-soft) !important;
  transform: scale(1.1) !important;
}

/* === 加载状态设计 === */
.waline-wrapper-premium .wl-loading {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: var(--waline-space-2xl) !important;
  color: var(--waline-text-tertiary) !important;
}

.waline-wrapper-premium .wl-loading::before {
  content: '';
  width: 24px;
  height: 24px;
  border: 2px solid var(--waline-border-subtle);
  border-top-color: var(--waline-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: var(--waline-space-sm);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* === 分页设计 === */
.waline-wrapper-premium .wl-operation {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  gap: var(--waline-space-md) !important;
  margin-top: var(--waline-space-2xl) !important;
  padding-top: var(--waline-space-xl) !important;
  border-top: 1px solid var(--waline-border-subtle) !important;
}

.waline-wrapper-premium .wl-operation .wl-btn {
  background: var(--waline-surface-secondary) !important;
  border: 1px solid var(--waline-border-subtle) !important;
  color: var(--waline-text-secondary) !important;
  padding: var(--waline-space-sm) var(--waline-space-lg) !important;
  border-radius: var(--waline-radius-md) !important;
  font-size: 0.875rem !important;
  cursor: pointer !important;
  transition: all var(--waline-transition-medium) !important;
  font-weight: 500 !important;
}

.waline-wrapper-premium .wl-operation .wl-btn:hover {
  background: var(--waline-primary) !important;
  border-color: var(--waline-primary) !important;
  color: var(--waline-text-inverse) !important;
  transform: translateY(-1px) !important;
  box-shadow: var(--waline-shadow-medium) !important;
}

.waline-wrapper-premium .wl-operation .wl-btn:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  transform: none !important;
}

/* === 统计信息设计 === */
.waline-wrapper-premium .wl-count {
  text-align: center !important;
  padding: var(--waline-space-lg) !important;
  color: var(--waline-text-tertiary) !important;
  font-size: 0.875rem !important;
  border-bottom: 1px solid var(--waline-border-subtle) !important;
  margin-bottom: var(--waline-space-xl) !important;
  background: var(--waline-surface-primary) !important;
  border-radius: var(--waline-radius-lg) !important;
}

/* === 空状态设计 === */
.waline-wrapper-premium .wl-empty {
  text-align: center !important;
  padding: var(--waline-space-3xl) var(--waline-space-xl) !important;
  color: var(--waline-text-tertiary) !important;
}

.waline-wrapper-premium .wl-empty::before {
  content: '💬';
  display: block;
  font-size: 3rem;
  margin-bottom: var(--waline-space-lg);
  opacity: 0.5;
}

/* === 错误状态设计 === */
.waline-wrapper-premium .wl-error {
  background: rgba(239, 68, 68, 0.1) !important;
  border: 1px solid rgba(239, 68, 68, 0.2) !important;
  color: #dc2626 !important;
  padding: var(--waline-space-lg) !important;
  border-radius: var(--waline-radius-lg) !important;
  margin-bottom: var(--waline-space-lg) !important;
  text-align: center !important;
}

/* === 成功状态设计 === */
.waline-wrapper-premium .wl-success {
  background: rgba(34, 197, 94, 0.1) !important;
  border: 1px solid rgba(34, 197, 94, 0.2) !important;
  color: #16a34a !important;
  padding: var(--waline-space-lg) !important;
  border-radius: var(--waline-radius-lg) !important;
  margin-bottom: var(--waline-space-lg) !important;
  text-align: center !important;
}

/* === 自定义CSS变量覆盖 === */
.waline-wrapper-premium {
  --waline-font-size: 1rem;
  --waline-white: var(--waline-surface-primary);
  --waline-light-grey: var(--waline-text-tertiary);
  --waline-dark-grey: var(--waline-text-secondary);
  --waline-theme-color: var(--waline-primary);
  --waline-active-color: var(--waline-primary-strong);
  --waline-color: var(--waline-text-primary);
  --waline-bg-color: var(--waline-surface-primary);
  --waline-bg-color-light: var(--waline-surface-secondary);
  --waline-bg-color-hover: var(--waline-primary-soft);
  --waline-border-color: var(--waline-border-subtle);
  --waline-disable-bg-color: var(--waline-surface-secondary);
  --waline-disable-color: var(--waline-text-tertiary);
  --waline-avatar-size: 3rem;
  --waline-m-avatar-size: 2.5rem;
  --waline-badge-color: var(--waline-primary);
  --waline-info-bg-color: var(--waline-surface-secondary);
  --waline-info-color: var(--waline-text-tertiary);
  --waline-border-radius: var(--waline-radius-lg);
  --waline-box-shadow: var(--waline-shadow-subtle);
}

/* === 最终优化和微调 === */
.waline-wrapper-premium * {
  box-sizing: border-box !important;
}

.waline-wrapper-premium .wl-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
}

/* === 用户登录状态优化 === */

/* 隐藏默认的用户信息显示区域 */
.waline-wrapper-premium .wl-header .wl-user {
  display: none !important;
}

/* 优化的用户登录状态栏 */
.waline-wrapper-premium .wl-user-status {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  padding: var(--waline-space-md) var(--waline-space-lg) !important;
  background: linear-gradient(
    135deg,
    var(--waline-surface-primary) 0%,
    var(--waline-surface-secondary) 100%
  ) !important;
  border: 1px solid var(--waline-border-subtle) !important;
  border-radius: var(--waline-radius-lg) !important;
  margin-bottom: var(--waline-space-lg) !important;
  box-shadow: var(--waline-shadow-subtle) !important;
  transition: all var(--waline-transition-medium) !important;
}

/* 用户信息区域 */
.waline-wrapper-premium .wl-user-info {
  display: flex !important;
  align-items: center !important;
  gap: var(--waline-space-md) !important;
}

/* 优化的用户头像 */
.waline-wrapper-premium .wl-user-avatar {
  width: 36px !important;
  height: 36px !important;
  border-radius: 50% !important;
  border: 2px solid var(--waline-border-medium) !important;
  transition: all var(--waline-transition-medium) !important;
  box-shadow: var(--waline-shadow-subtle) !important;
  object-fit: cover !important;
}

.waline-wrapper-premium .wl-user-avatar:hover {
  border-color: var(--waline-primary) !important;
  box-shadow:
    var(--waline-shadow-medium),
    0 0 0 2px var(--waline-primary-soft) !important;
  transform: scale(1.05) !important;
}

/* 用户名和状态 */
.waline-wrapper-premium .wl-user-name {
  font-weight: 600 !important;
  color: var(--waline-text-primary) !important;
  font-size: 0.875rem !important;
  margin: 0 !important;
}

.waline-wrapper-premium .wl-user-email {
  font-size: 0.75rem !important;
  color: var(--waline-text-tertiary) !important;
  margin: 0 !important;
}

/* 登录/退出按钮组 */
.waline-wrapper-premium .wl-auth-actions {
  display: flex !important;
  align-items: center !important;
  gap: var(--waline-space-sm) !important;
}

.waline-wrapper-premium .wl-auth-btn {
  background: transparent !important;
  border: 1px solid var(--waline-border-medium) !important;
  color: var(--waline-text-secondary) !important;
  padding: var(--waline-space-xs) var(--waline-space-md) !important;
  border-radius: var(--waline-radius-md) !important;
  font-size: 0.75rem !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all var(--waline-transition-fast) !important;
  text-decoration: none !important;
  display: inline-flex !important;
  align-items: center !important;
  gap: var(--waline-space-xs) !important;
}

.waline-wrapper-premium .wl-auth-btn:hover {
  background: var(--waline-primary-soft) !important;
  border-color: var(--waline-primary-medium) !important;
  color: var(--waline-primary) !important;
  transform: translateY(-1px) !important;
}

.waline-wrapper-premium .wl-auth-btn.primary {
  background: var(--waline-primary) !important;
  border-color: var(--waline-primary) !important;
  color: var(--waline-text-inverse) !important;
}

.waline-wrapper-premium .wl-auth-btn.primary:hover {
  background: var(--waline-primary-strong) !important;
  border-color: var(--waline-primary-strong) !important;
  color: var(--waline-text-inverse) !important;
}

/* 登录状态指示器 */
.waline-wrapper-premium .wl-status-indicator {
  width: 8px !important;
  height: 8px !important;
  border-radius: 50% !important;
  margin-right: var(--waline-space-xs) !important;
}

.waline-wrapper-premium .wl-status-indicator.online {
  background: #22c55e !important;
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2) !important;
}

.waline-wrapper-premium .wl-status-indicator.offline {
  background: var(--waline-text-tertiary) !important;
}

/* 隐藏原始的登录相关元素 */
.waline-wrapper-premium .wl-login,
.waline-wrapper-premium .wl-logout,
.waline-wrapper-premium .wl-header .wl-login-info {
  display: none !important;
}

/* 优化输入区域，移除用户头像显示 */
.waline-wrapper-premium .wl-panel .wl-user {
  display: none !important;
}

/* 确保评论列表中的用户头像正常显示 */
.waline-wrapper-premium .wl-card .wl-user,
.waline-wrapper-premium .wl-item .wl-user,
.waline-wrapper-premium .wl-comment .wl-user {
  display: flex !important;
}

/* 更强力地隐藏输入区域的用户显示 */
.waline-wrapper-premium .wl-panel .wl-user,
.waline-wrapper-premium .wl-header .wl-user,
.waline-wrapper-premium > .wl-user,
.waline-wrapper-premium .wl-editor .wl-user,
.waline-wrapper-premium .wl-user:not(.wl-card .wl-user):not(.wl-item .wl-user):not(.wl-comment .wl-user) {
  display: none !important;
  visibility: hidden !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
  position: absolute !important;
  left: -9999px !important;
}

/* 特别针对可能出现在状态栏下方的用户头像 */
.waline-wrapper-premium .wl-user-status-enhanced + .wl-user,
.waline-wrapper-premium .wl-user-status + .wl-user {
  display: none !important;
  visibility: hidden !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .waline-wrapper-premium .wl-user-status {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: var(--waline-space-md) !important;
  }

  .waline-wrapper-premium .wl-auth-actions {
    width: 100% !important;
    justify-content: flex-end !important;
  }

  .waline-wrapper-premium .wl-user-email {
    display: none !important;
  }
}

/* === 使用Waline官方CSS变量系统美化原生用户显示 === */

/* 重新定义Waline CSS变量以实现红点奖级别设计 */
.waline-wrapper-premium {
  /* 头像尺寸 - 使用更大的头像 */
  --waline-avatar-size: 4rem !important;
  --waline-m-avatar-size: 3rem !important;

  /* 头像样式 */
  --waline-avatar-radius: 50% !important;
  --waline-box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;

  /* 边框样式 */
  --waline-border: 2px solid hsl(var(--primary) / 0.2) !important;
  --waline-border-color: hsl(var(--border) / 0.3) !important;

  /* 背景颜色 */
  --waline-bg-color: hsl(var(--background)) !important;
  --waline-bg-color-light: hsl(var(--card)) !important;
  --waline-bg-color-hover: hsl(var(--primary) / 0.05) !important;

  /* 文字颜色 */
  --waline-color: hsl(var(--foreground)) !important;
  --waline-light-grey: hsl(var(--muted-foreground)) !important;
  --waline-dark-grey: hsl(var(--foreground) / 0.8) !important;

  /* 主题色 */
  --waline-theme-color: hsl(var(--primary)) !important;
  --waline-active-color: hsl(var(--primary) / 0.9) !important;
}

/* 美化原生用户显示区域 */
.waline-wrapper-premium .wl-user {
  background: linear-gradient(135deg,
    hsl(var(--card) / 0.8) 0%,
    hsl(var(--card) / 0.95) 100%) !important;
  backdrop-filter: blur(12px) !important;
  border: 1px solid hsl(var(--border) / 0.5) !important;
  border-radius: 16px !important;
  padding: 20px 24px !important;
  margin-bottom: 24px !important;
  box-shadow:
    0 8px 32px hsl(var(--foreground) / 0.08),
    0 4px 16px hsl(var(--foreground) / 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;
  display: flex !important;
  align-items: center !important;
  gap: 20px !important;
}

/* 添加装饰性背景 */
.waline-wrapper-premium .wl-user::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    hsl(var(--primary) / 0.02) 0%,
    transparent 50%,
    hsl(var(--primary) / 0.02) 100%);
  pointer-events: none;
  z-index: 0;
}

/* 悬停效果 */
.waline-wrapper-premium .wl-user:hover {
  transform: translateY(-3px) !important;
  box-shadow:
    0 12px 40px hsl(var(--foreground) / 0.12),
    0 6px 20px hsl(var(--foreground) / 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
  border-color: hsl(var(--primary) / 0.4) !important;
}

/* 美化头像 */
.waline-wrapper-premium .wl-user .wl-avatar {
  width: var(--waline-avatar-size) !important;
  height: var(--waline-avatar-size) !important;
  border-radius: var(--waline-avatar-radius) !important;
  border: 3px solid hsl(var(--primary) / 0.2) !important;
  box-shadow:
    0 4px 16px hsl(var(--primary) / 0.2),
    0 0 0 1px hsl(var(--background)) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  cursor: pointer !important;
  position: relative !important;
  z-index: 2 !important;
  object-fit: cover !important;
}

/* 在线状态指示器 */
.waline-wrapper-premium .wl-user .wl-avatar::after {
  content: '';
  position: absolute;
  top: -3px;
  right: -3px;
  width: 18px;
  height: 18px;
  background: linear-gradient(135deg, #22c55e, #16a34a);
  border: 3px solid hsl(var(--background));
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(34, 197, 94, 0.4);
  animation: pulse-online 2s infinite;
  z-index: 3;
}

/* 头像悬停效果 */
.waline-wrapper-premium .wl-user .wl-avatar:hover {
  transform: scale(1.05) !important;
  border-color: hsl(var(--primary) / 0.5) !important;
  box-shadow:
    0 6px 24px hsl(var(--primary) / 0.3),
    0 0 0 1px hsl(var(--background)) !important;
}

/* 美化用户名 */
.waline-wrapper-premium .wl-user .wl-nick {
  font-size: 1.25rem !important;
  font-weight: 700 !important;
  color: hsl(var(--foreground)) !important;
  margin: 0 0 4px 0 !important;
  background: linear-gradient(135deg,
    hsl(var(--foreground)),
    hsl(var(--primary))) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  position: relative !important;
  z-index: 2 !important;
  text-decoration: none !important;
}

/* 添加登录状态文字 */
.waline-wrapper-premium .wl-user .wl-nick::after {
  content: '已登录';
  position: absolute;
  bottom: -20px;
  left: 0;
  font-size: 0.75rem;
  font-weight: 500;
  color: #22c55e;
  background: rgba(34, 197, 94, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid rgba(34, 197, 94, 0.2);
  -webkit-background-clip: initial !important;
  -webkit-text-fill-color: initial !important;
  background-clip: initial !important;
}

/* 在线状态动画 */
@keyframes pulse-online {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .waline-wrapper-premium {
    --waline-avatar-size: 3rem !important;
    --waline-m-avatar-size: 2.5rem !important;
  }

  .waline-wrapper-premium .wl-user {
    padding: 16px 20px !important;
    gap: 16px !important;
  }

  .waline-wrapper-premium .wl-user .wl-nick {
    font-size: 1.125rem !important;
  }

  .waline-wrapper-premium .wl-user .wl-avatar::after {
    width: 14px;
    height: 14px;
    border-width: 2px;
  }
}

/* === 打印样式优化 === */
@media print {
  .waline-wrapper-premium {
    box-shadow: none !important;
    border: 1px solid #ccc !important;
    background: white !important;
  }

  .waline-wrapper-premium .wl-panel,
  .waline-wrapper-premium .wl-action,
  .waline-wrapper-premium .wl-user-status {
    display: none !important;
  }

  .wl-user-enhanced {
    background: white !important;
    border: 1px solid #ccc !important;
    box-shadow: none !important;
  }
}
